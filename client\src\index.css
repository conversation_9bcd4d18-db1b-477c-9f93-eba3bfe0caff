@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(220, 20%, 97%);
  --foreground: hsl(220, 15%, 15%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(220, 15%, 15%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(220, 15%, 15%);
  --primary: hsl(217, 78%, 51%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --muted: hsl(220, 14%, 96%);
  --muted-foreground: hsl(220, 13%, 46%);
  --accent: hsl(217, 78%, 51%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --ring: hsl(217, 78%, 51%);
  --chart-1: hsl(12, 76%, 61%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(27, 87%, 67%);
  --sidebar: hsl(220, 14%, 96%);
  --sidebar-foreground: hsl(220, 15%, 15%);
  --sidebar-primary: hsl(217, 78%, 51%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(217, 78%, 51%);
  --sidebar-accent-foreground: hsl(0, 0%, 100%);
  --sidebar-border: hsl(220, 13%, 91%);
  --sidebar-ring: hsl(217, 78%, 51%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 8px;
}

.dark {
  --background: hsl(220, 20%, 4%);
  --foreground: hsl(220, 15%, 85%);
  --card: hsl(220, 20%, 8%);
  --card-foreground: hsl(220, 15%, 85%);
  --popover: hsl(220, 20%, 8%);
  --popover-foreground: hsl(220, 15%, 85%);
  --primary: hsl(217, 78%, 51%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(220, 14%, 96%);
  --secondary-foreground: hsl(220, 15%, 15%);
  --muted: hsl(220, 20%, 12%);
  --muted-foreground: hsl(220, 13%, 54%);
  --accent: hsl(217, 78%, 51%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(220, 20%, 18%);
  --input: hsl(220, 20%, 18%);
  --ring: hsl(217, 78%, 51%);
  --chart-1: hsl(220, 70%, 50%);
  --chart-2: hsl(160, 60%, 45%);
  --chart-3: hsl(30, 80%, 55%);
  --chart-4: hsl(280, 65%, 60%);
  --chart-5: hsl(340, 75%, 55%);
  --sidebar: hsl(220, 20%, 12%);
  --sidebar-foreground: hsl(220, 15%, 85%);
  --sidebar-primary: hsl(217, 78%, 51%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(217, 78%, 51%);
  --sidebar-accent-foreground: hsl(0, 0%, 100%);
  --sidebar-border: hsl(220, 20%, 18%);
  --sidebar-ring: hsl(217, 78%, 51%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

.kanban-column {
  min-height: 500px;
}

.task-card {
  transition: all 0.2s ease;
}

.task-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.priority-high { 
  border-left: 4px solid hsl(var(--destructive)); 
}

.priority-medium { 
  border-left: 4px solid hsl(var(--chart-4)); 
}

.priority-low { 
  border-left: 4px solid hsl(var(--chart-2)); 
}

.status-todo { 
  background: hsl(var(--muted)); 
}

.status-progress { 
  background: hsl(var(--primary)); 
}

.status-done { 
  background: hsl(var(--chart-2)); 
}

.status-overdue { 
  background: hsl(var(--destructive)); 
}
