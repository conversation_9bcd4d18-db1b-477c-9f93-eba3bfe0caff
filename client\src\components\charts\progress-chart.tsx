import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Toolt<PERSON> } from "recharts";

const data = [
  { name: "To Do", value: 25, color: "#94a3b8" },
  { name: "In Progress", value: 35, color: "#3b82f6" },
  { name: "<PERSON>", value: 15, color: "#8b5cf6" },
  { name: "Done", value: 40, color: "#10b981" },
];

export function ProgressChart() {
  return (
    <div className="h-64 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={80}
            paddingAngle={5}
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value) => [`${value}%`, "Tasks"]}
            labelStyle={{ color: "#000" }}
          />
          <Legend />
        </Pie<PERSON><PERSON>>
      </ResponsiveContainer>
    </div>
  );
}
