import {
  users,
  projects,
  tasks,
  projectMembers,
  taskComments,
  notifications,
  type User,
  type InsertUser,
  type Project,
  type InsertProject,
  type Task,
  type InsertTask,
  type ProjectMember,
  type InsertProjectMember,
  type TaskComment,
  type InsertTaskComment,
  type Notification,
  type InsertNotification,
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, asc, count, sql } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: string, updates: Partial<InsertUser>): Promise<User>;

  // Project operations
  getProjects(userId: string): Promise<(Project & { owner: User; memberCount: number; taskCount: number })[]>;
  getProject(id: string): Promise<(Project & { owner: User; members: (ProjectMember & { user: User })[] }) | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: string, updates: Partial<InsertProject>): Promise<Project>;
  deleteProject(id: string): Promise<void>;

  // Project member operations
  addProjectMember(member: InsertProjectMember): Promise<ProjectMember>;
  removeProjectMember(projectId: string, userId: string): Promise<void>;
  getProjectMembers(projectId: string): Promise<(ProjectMember & { user: User })[]>;

  // Task operations
  getTasks(projectId?: string, assigneeId?: string): Promise<(Task & { assignee?: User; project: Project })[]>;
  getTask(id: string): Promise<(Task & { assignee?: User; project: Project; comments: (TaskComment & { user: User })[] }) | undefined>;
  createTask(task: InsertTask): Promise<Task>;
  updateTask(id: string, updates: Partial<InsertTask>): Promise<Task>;
  deleteTask(id: string): Promise<void>;

  // Task comment operations
  addTaskComment(comment: InsertTaskComment): Promise<TaskComment>;
  getTaskComments(taskId: string): Promise<(TaskComment & { user: User })[]>;

  // Notification operations
  getNotifications(userId: string): Promise<Notification[]>;
  createNotification(notification: InsertNotification): Promise<Notification>;
  markNotificationAsRead(id: string): Promise<void>;
  markAllNotificationsAsRead(userId: string): Promise<void>;

  // Dashboard statistics
  getDashboardStats(userId: string): Promise<{
    activeProjects: number;
    completedTasks: number;
    inProgressTasks: number;
    overdueTasks: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values({
        ...insertUser,
        updatedAt: new Date(),
      })
      .returning();
    return user;
  }

  async updateUser(id: string, updates: Partial<InsertUser>): Promise<User> {
    const [user] = await db
      .update(users)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async getProjects(userId: string): Promise<(Project & { owner: User; memberCount: number; taskCount: number })[]> {
    const result = await db
      .select({
        project: projects,
        owner: users,
        memberCount: count(projectMembers.id),
        taskCount: count(tasks.id),
      })
      .from(projects)
      .leftJoin(users, eq(projects.ownerId, users.id))
      .leftJoin(projectMembers, eq(projects.id, projectMembers.projectId))
      .leftJoin(tasks, eq(projects.id, tasks.projectId))
      .where(
        sql`${projects.ownerId} = ${userId} OR ${projects.id} IN (
          SELECT project_id FROM project_members WHERE user_id = ${userId}
        )`
      )
      .groupBy(projects.id, users.id)
      .orderBy(desc(projects.updatedAt));

    return result.map(row => ({
      ...row.project,
      owner: row.owner!,
      memberCount: Number(row.memberCount),
      taskCount: Number(row.taskCount),
    }));
  }

  async getProject(id: string): Promise<(Project & { owner: User; members: (ProjectMember & { user: User })[] }) | undefined> {
    const [projectData] = await db
      .select({
        project: projects,
        owner: users,
      })
      .from(projects)
      .leftJoin(users, eq(projects.ownerId, users.id))
      .where(eq(projects.id, id));

    if (!projectData?.project) return undefined;

    const members = await db
      .select({
        member: projectMembers,
        user: users,
      })
      .from(projectMembers)
      .leftJoin(users, eq(projectMembers.userId, users.id))
      .where(eq(projectMembers.projectId, id));

    return {
      ...projectData.project,
      owner: projectData.owner!,
      members: members.map(m => ({ ...m.member, user: m.user! })),
    };
  }

  async createProject(project: InsertProject): Promise<Project> {
    const [newProject] = await db
      .insert(projects)
      .values({
        ...project,
        updatedAt: new Date(),
      })
      .returning();
    return newProject;
  }

  async updateProject(id: string, updates: Partial<InsertProject>): Promise<Project> {
    const [project] = await db
      .update(projects)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(projects.id, id))
      .returning();
    return project;
  }

  async deleteProject(id: string): Promise<void> {
    await db.delete(projects).where(eq(projects.id, id));
  }

  async addProjectMember(member: InsertProjectMember): Promise<ProjectMember> {
    const [newMember] = await db
      .insert(projectMembers)
      .values(member)
      .returning();
    return newMember;
  }

  async removeProjectMember(projectId: string, userId: string): Promise<void> {
    await db
      .delete(projectMembers)
      .where(
        and(
          eq(projectMembers.projectId, projectId),
          eq(projectMembers.userId, userId)
        )
      );
  }

  async getProjectMembers(projectId: string): Promise<(ProjectMember & { user: User })[]> {
    const result = await db
      .select({
        member: projectMembers,
        user: users,
      })
      .from(projectMembers)
      .leftJoin(users, eq(projectMembers.userId, users.id))
      .where(eq(projectMembers.projectId, projectId));

    return result.map(row => ({ ...row.member, user: row.user! }));
  }

  async getTasks(projectId?: string, assigneeId?: string): Promise<(Task & { assignee?: User; project: Project })[]> {
    const baseQuery = db
      .select({
        task: tasks,
        assignee: users,
        project: projects,
      })
      .from(tasks)
      .leftJoin(users, eq(tasks.assigneeId, users.id))
      .leftJoin(projects, eq(tasks.projectId, projects.id));

    let whereConditions = [];
    if (projectId) {
      whereConditions.push(eq(tasks.projectId, projectId));
    }
    if (assigneeId) {
      whereConditions.push(eq(tasks.assigneeId, assigneeId));
    }

    const query = whereConditions.length > 0 
      ? baseQuery.where(whereConditions.length === 1 ? whereConditions[0] : and(...whereConditions))
      : baseQuery;

    const result = await query.orderBy(desc(tasks.updatedAt));

    return result.map(row => ({
      ...row.task,
      assignee: row.assignee || undefined,
      project: row.project!,
    }));
  }

  async getTask(id: string): Promise<(Task & { assignee?: User; project: Project; comments: (TaskComment & { user: User })[] }) | undefined> {
    const [taskData] = await db
      .select({
        task: tasks,
        assignee: users,
        project: projects,
      })
      .from(tasks)
      .leftJoin(users, eq(tasks.assigneeId, users.id))
      .leftJoin(projects, eq(tasks.projectId, projects.id))
      .where(eq(tasks.id, id));

    if (!taskData?.task) return undefined;

    const comments = await this.getTaskComments(id);

    return {
      ...taskData.task,
      assignee: taskData.assignee || undefined,
      project: taskData.project!,
      comments,
    };
  }

  async createTask(task: InsertTask): Promise<Task> {
    const [newTask] = await db
      .insert(tasks)
      .values({
        ...task,
        updatedAt: new Date(),
      })
      .returning();
    return newTask;
  }

  async updateTask(id: string, updates: Partial<InsertTask>): Promise<Task> {
    const [task] = await db
      .update(tasks)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(tasks.id, id))
      .returning();
    return task;
  }

  async deleteTask(id: string): Promise<void> {
    await db.delete(tasks).where(eq(tasks.id, id));
  }

  async addTaskComment(comment: InsertTaskComment): Promise<TaskComment> {
    const [newComment] = await db
      .insert(taskComments)
      .values(comment)
      .returning();
    return newComment;
  }

  async getTaskComments(taskId: string): Promise<(TaskComment & { user: User })[]> {
    const result = await db
      .select({
        comment: taskComments,
        user: users,
      })
      .from(taskComments)
      .leftJoin(users, eq(taskComments.userId, users.id))
      .where(eq(taskComments.taskId, taskId))
      .orderBy(asc(taskComments.createdAt));

    return result.map(row => ({ ...row.comment, user: row.user! }));
  }

  async getNotifications(userId: string): Promise<Notification[]> {
    return await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, userId))
      .orderBy(desc(notifications.createdAt));
  }

  async createNotification(notification: InsertNotification): Promise<Notification> {
    const [newNotification] = await db
      .insert(notifications)
      .values(notification)
      .returning();
    return newNotification;
  }

  async markNotificationAsRead(id: string): Promise<void> {
    await db
      .update(notifications)
      .set({ isRead: true })
      .where(eq(notifications.id, id));
  }

  async markAllNotificationsAsRead(userId: string): Promise<void> {
    await db
      .update(notifications)
      .set({ isRead: true })
      .where(eq(notifications.userId, userId));
  }

  async getDashboardStats(userId: string): Promise<{
    activeProjects: number;
    completedTasks: number;
    inProgressTasks: number;
    overdueTasks: number;
  }> {
    // Get projects where user is owner or member
    const userProjects = await db
      .select({ id: projects.id })
      .from(projects)
      .where(
        sql`${projects.ownerId} = ${userId} OR ${projects.id} IN (
          SELECT project_id FROM project_members WHERE user_id = ${userId}
        )`
      );

    const projectIds = userProjects.map(p => p.id);

    if (projectIds.length === 0) {
      return {
        activeProjects: 0,
        completedTasks: 0,
        inProgressTasks: 0,
        overdueTasks: 0,
      };
    }

    const [activeProjectsCount] = await db
      .select({ count: count() })
      .from(projects)
      .where(
        and(
          sql`${projects.id} = ANY(${projectIds})`,
          eq(projects.status, "active")
        )
      );

    const [completedTasksCount] = await db
      .select({ count: count() })
      .from(tasks)
      .where(
        and(
          sql`${tasks.projectId} = ANY(${projectIds})`,
          eq(tasks.status, "done")
        )
      );

    const [inProgressTasksCount] = await db
      .select({ count: count() })
      .from(tasks)
      .where(
        and(
          sql`${tasks.projectId} = ANY(${projectIds})`,
          eq(tasks.status, "in_progress")
        )
      );

    const [overdueTasksCount] = await db
      .select({ count: count() })
      .from(tasks)
      .where(
        and(
          sql`${tasks.projectId} = ANY(${projectIds})`,
          eq(tasks.status, "overdue")
        )
      );

    return {
      activeProjects: Number(activeProjectsCount.count),
      completedTasks: Number(completedTasksCount.count),
      inProgressTasks: Number(inProgressTasksCount.count),
      overdueTasks: Number(overdueTasksCount.count),
    };
  }
}

export const storage = new DatabaseStorage();
