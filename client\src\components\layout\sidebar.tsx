import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
// import { useAuth, useLogout } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { 
  Home, 
  FolderOpen, 
  CheckSquare, 
  Users, 
  BarChart3, 
  Bell, 
  User, 
  LogOut,
  Share2
} from "lucide-react";

const navigation = [
  { name: "Dashboard", href: "/", icon: Home },
  { name: "Projects", href: "/projects", icon: FolderOpen },
  { name: "Task Board", href: "/tasks", icon: CheckSquare },
  { name: "Team", href: "/team", icon: Users },
  { name: "Workflows", href: "/workflows", icon: Share2 },
  { name: "Reports", href: "/reports", icon: BarChart3 },
  { name: "Notifications", href: "/notifications", icon: Bell },
];

export default function Sidebar() {
  const [location] = useLocation();
  // const { user } = useAuth();
  // const logoutMutation = useLogout();

  // Mock user for demo
  const user = {
    firstName: "Demo",
    lastName: "User",
    username: "demouser",
    role: "leader"
  };

  const handleLogout = () => {
    // logoutMutation.mutate();
    console.log("Logout clicked (demo mode)");
  };

  const getUserInitials = (user: any) => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user?.username) {
      return user.username.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  return (
    <aside className="w-64 bg-card border-r border-border flex flex-col">
      <div className="p-6 border-b border-border">
        <h1 className="text-xl font-bold text-primary">TeamFlow</h1>
        <p className="text-sm text-muted-foreground">Workspace</p>
      </div>
      
      <nav className="flex-1 p-4 space-y-1">
        {navigation.map((item) => {
          const isActive = location === item.href || (item.href !== "/" && location.startsWith(item.href));
          return (
            <Link key={item.name} href={item.href}>
              <a
                className={cn(
                  "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  isActive
                    ? "bg-accent text-accent-foreground"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground"
                )}
                data-testid={`nav-${item.name.toLowerCase().replace(" ", "-")}`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </a>
            </Link>
          );
        })}
      </nav>
      
      <div className="p-4 border-t border-border">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-medium text-sm">
            {getUserInitials(user)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate" data-testid="user-name">
              {user?.firstName && user?.lastName 
                ? `${user.firstName} ${user.lastName}` 
                : user?.username || "User"
              }
            </p>
            <p className="text-xs text-muted-foreground" data-testid="user-role">
              {user?.role === "leader" ? "Team Leader" : "Employee"}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Link href="/profile">
            <Button variant="ghost" size="sm" className="flex-1" data-testid="button-profile">
              <User className="w-4 h-4 mr-2" />
              Profile
            </Button>
          </Link>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLogout}
            data-testid="button-logout"
          >
            <LogOut className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </aside>
  );
}
