# Overview

This is a full-stack team project management application called "TeamFlow" built with React, Express.js, TypeScript, and PostgreSQL. The application provides comprehensive project and task management capabilities for teams, featuring role-based access control for leaders and employees. The system includes authentication, project management, task tracking with Kanban boards, team management, notifications, and reporting features.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture

**Framework**: React 18 with TypeScript using Vite as the build tool
- **UI Library**: Shadcn/ui components built on Radix UI primitives with Tailwind CSS for styling
- **Routing**: Wouter for client-side routing with role-based route protection
- **State Management**: TanStack Query (React Query) for server state management and caching
- **Form Handling**: React Hook Form with Zod validation schemas
- **Authentication**: JWT-based authentication with token storage in localStorage

**Key Design Patterns**:
- Component-based architecture with reusable UI components
- Custom hooks for authentication and data fetching
- Layout components (Sidebar, Header) for consistent page structure
- Separate page components for different application sections

## Backend Architecture

**Framework**: Express.js with TypeScript running on Node.js
- **Database ORM**: Drizzle ORM for type-safe database operations
- **Authentication**: JWT tokens with bcrypt for password hashing
- **API Design**: RESTful API structure with middleware for authentication
- **File Structure**: Separation of concerns with dedicated files for routes, storage layer, and database configuration

**Key Components**:
- Storage abstraction layer for database operations
- Middleware for JWT authentication and request logging
- Route handlers for authentication, projects, tasks, and team management
- Database schema definitions with proper relationships and enums

## Database Design

**PostgreSQL Database** with the following core entities:
- **Users**: Authentication and profile information with role-based access (leader/employee)
- **Projects**: Project management with ownership, status tracking, and progress monitoring
- **Tasks**: Task management with status workflow, priority levels, and assignee relationships
- **Project Members**: Many-to-many relationship between users and projects
- **Task Comments**: Threaded comments on tasks for collaboration
- **Notifications**: Real-time notification system for team updates

**Schema Features**:
- UUID primary keys for security
- Proper foreign key relationships and constraints
- Enumerated types for status, priority, and role fields
- Timestamp tracking for audit trails

## Authentication & Authorization

**JWT-based Authentication**:
- Secure password hashing using bcrypt
- Role-based access control (leader vs employee permissions)
- Token-based session management
- Protected routes on both frontend and backend

## State Management

**Client-side State**:
- TanStack Query for server state caching and synchronization
- Local component state using React hooks
- Authentication state management through custom hooks
- Form state managed by React Hook Form

# External Dependencies

## Database & Storage
- **Neon Database**: PostgreSQL database hosting with connection pooling
- **Drizzle ORM**: Type-safe database operations and migrations
- **Connect PG Simple**: PostgreSQL session store for Express

## UI & Styling
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Radix UI**: Headless UI components for accessibility
- **Shadcn/ui**: Pre-built component library
- **Lucide React**: Icon library for consistent iconography
- **Recharts**: Chart library for data visualization

## Development & Build
- **Vite**: Fast build tool and development server
- **TypeScript**: Static type checking
- **ESBuild**: Fast JavaScript bundler for production builds
- **PostCSS**: CSS processing with Tailwind integration

## Authentication & Security
- **bcrypt**: Password hashing library
- **jsonwebtoken**: JWT token generation and verification
- **Zod**: Runtime type validation for forms and API data

## Data Fetching & Forms
- **TanStack Query**: Server state management and caching
- **React Hook Form**: Performant form library
- **Hookform Resolvers**: Integration between React Hook Form and Zod

## Deployment & Runtime
- **WebSocket (ws)**: Real-time communication support
- **Express**: Web application framework
- **Replit Plugins**: Development environment integration