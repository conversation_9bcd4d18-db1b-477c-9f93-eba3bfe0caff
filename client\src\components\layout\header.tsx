import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Bell, Plus } from "lucide-react";
// import { useQuery } from "@tanstack/react-query";
// import { authenticatedApiRequest } from "@/lib/auth";

const pageTitles: Record<string, { title: string; subtitle: string }> = {
  "/": { title: "Dashboard", subtitle: "Overview of your projects and tasks" },
  "/projects": { title: "Projects", subtitle: "Manage your team's projects and deliverables" },
  "/tasks": { title: "Task Board", subtitle: "Manage and track your team's tasks" },
  "/team": { title: "Team Management", subtitle: "Manage team members and their permissions" },
  "/workflows": { title: "Workflow Automation", subtitle: "Create custom workflows between your favorite apps" },
  "/reports": { title: "Reports & Analytics", subtitle: "Track progress and team performance" },
  "/notifications": { title: "Notifications", subtitle: "Stay updated with your team's activities" },
  "/profile": { title: "Profile Settings", subtitle: "Manage your personal information and preferences" },
};

export default function Header() {
  const [location] = useLocation();
  const pageInfo = pageTitles[location] || { title: "TeamFlow", subtitle: "" };

  // Temporary: Mock notifications for demo
  // const { data: notifications } = useQuery({
  //   queryKey: ["notifications"],
  //   queryFn: async () => {
  //     const response = await authenticatedApiRequest("GET", "/api/notifications");
  //     return response.json();
  //   },
  // });

  const unreadCount = 3; // Mock unread count

  return (
    <header className="bg-card border-b border-border p-4 flex items-center justify-between">
      <div>
        <h2 className="text-2xl font-semibold" data-testid="page-title">
          {pageInfo.title}
        </h2>
        {pageInfo.subtitle && (
          <p className="text-muted-foreground" data-testid="page-subtitle">
            {pageInfo.subtitle}
          </p>
        )}
      </div>
      <div className="flex items-center space-x-4">
        <Button 
          variant="ghost" 
          size="sm" 
          className="relative"
          data-testid="button-notifications"
        >
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs w-5 h-5 rounded-full flex items-center justify-center">
              {unreadCount > 9 ? "9+" : unreadCount}
            </span>
          )}
        </Button>
        <Button size="sm" data-testid="button-create">
          <Plus className="w-4 h-4 mr-2" />
          Create
        </Button>
      </div>
    </header>
  );
}
