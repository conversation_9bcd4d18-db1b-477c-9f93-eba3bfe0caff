import { use<PERSON>ara<PERSON> } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { authenticatedApiRequest } from "@/lib/auth";
import { ArrowLeft, Users, Calendar, Plus } from "lucide-react";
import { Link } from "wouter";

interface ProjectDetail {
  id: string;
  name: string;
  description?: string;
  status: string;
  startDate?: string;
  endDate?: string;
  progress: number;
  owner: {
    firstName?: string;
    lastName?: string;
    username: string;
  };
  members: Array<{
    user: {
      firstName?: string;
      lastName?: string;
      username: string;
      role: string;
    };
  }>;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: string;
  assignee?: {
    firstName?: string;
    lastName?: string;
    username: string;
  };
}

export default function ProjectDetail() {
  const params = useParams();
  const projectId = params.id;

  const { data: project, isLoading: projectLoading } = useQuery<ProjectDetail>({
    queryKey: ["projects", projectId],
    queryFn: async () => {
      const response = await authenticatedApiRequest("GET", `/api/projects/${projectId}`);
      return response.json();
    },
    enabled: !!projectId,
  });

  const { data: tasks, isLoading: tasksLoading } = useQuery<Task[]>({
    queryKey: ["tasks", "project", projectId],
    queryFn: async () => {
      const response = await authenticatedApiRequest("GET", `/api/tasks?projectId=${projectId}`);
      return response.json();
    },
    enabled: !!projectId,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "planning":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-gray-100 text-gray-800";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case "done":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "review":
        return "bg-purple-100 text-purple-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTaskStatus = (status: string) => {
    switch (status) {
      case "in_progress":
        return "In Progress";
      case "todo":
        return "To Do";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  if (projectLoading || tasksLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">Project not found</h3>
        <p className="text-muted-foreground mb-4">The project you're looking for doesn't exist.</p>
        <Link href="/projects">
          <Button>Back to Projects</Button>
        </Link>
      </div>
    );
  }

  const todoTasks = tasks?.filter(task => task.status === "todo") || [];
  const inProgressTasks = tasks?.filter(task => task.status === "in_progress") || [];
  const reviewTasks = tasks?.filter(task => task.status === "review") || [];
  const doneTasks = tasks?.filter(task => task.status === "done") || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/projects">
          <Button variant="ghost" size="sm" data-testid="button-back">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>

      {/* Project Info */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl mb-2" data-testid="project-title">
                {project.name}
              </CardTitle>
              {project.description && (
                <p className="text-muted-foreground" data-testid="project-description">
                  {project.description}
                </p>
              )}
            </div>
            <Badge className={getStatusColor(project.status)}>
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-2">Progress</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Completion</span>
                  <span>{project.progress}%</span>
                </div>
                <Progress value={project.progress} className="h-2" />
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Timeline</h4>
              <div className="space-y-1 text-sm">
                {project.startDate && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>Start: {new Date(project.startDate).toLocaleDateString()}</span>
                  </div>
                )}
                {project.endDate && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>End: {new Date(project.endDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Team</h4>
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-2 text-muted-foreground" />
                <span className="text-sm">{project.members.length} members</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tasks" data-testid="tab-tasks">Tasks</TabsTrigger>
          <TabsTrigger value="members" data-testid="tab-members">Team Members</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Project Tasks</h3>
            <Button size="sm" data-testid="button-create-task">
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          </div>

          {/* Kanban Board */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Todo Column */}
            <Card className="kanban-column">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    To Do
                  </span>
                  <Badge variant="secondary">{todoTasks.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {todoTasks.map((task) => (
                  <Card key={task.id} className="task-card priority-low" data-testid={`task-${task.id}`}>
                    <CardContent className="p-4">
                      <h5 className="font-medium mb-2">{task.title}</h5>
                      {task.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mb-3">
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </Badge>
                        {task.dueDate && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {task.assignee && (
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs text-primary-foreground mr-2">
                            {task.assignee.firstName?.[0] || task.assignee.username[0]}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {task.assignee.firstName && task.assignee.lastName
                              ? `${task.assignee.firstName} ${task.assignee.lastName}`
                              : task.assignee.username
                            }
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>

            {/* In Progress Column */}
            <Card className="kanban-column">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    In Progress
                  </span>
                  <Badge variant="secondary">{inProgressTasks.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {inProgressTasks.map((task) => (
                  <Card key={task.id} className="task-card priority-medium" data-testid={`task-${task.id}`}>
                    <CardContent className="p-4">
                      <h5 className="font-medium mb-2">{task.title}</h5>
                      {task.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mb-3">
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </Badge>
                        {task.dueDate && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {task.assignee && (
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs text-primary-foreground mr-2">
                            {task.assignee.firstName?.[0] || task.assignee.username[0]}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {task.assignee.firstName && task.assignee.lastName
                              ? `${task.assignee.firstName} ${task.assignee.lastName}`
                              : task.assignee.username
                            }
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>

            {/* Review Column */}
            <Card className="kanban-column">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                    Review
                  </span>
                  <Badge variant="secondary">{reviewTasks.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {reviewTasks.map((task) => (
                  <Card key={task.id} className="task-card priority-high" data-testid={`task-${task.id}`}>
                    <CardContent className="p-4">
                      <h5 className="font-medium mb-2">{task.title}</h5>
                      {task.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mb-3">
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </Badge>
                        {task.dueDate && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {task.assignee && (
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs text-primary-foreground mr-2">
                            {task.assignee.firstName?.[0] || task.assignee.username[0]}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {task.assignee.firstName && task.assignee.lastName
                              ? `${task.assignee.firstName} ${task.assignee.lastName}`
                              : task.assignee.username
                            }
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>

            {/* Done Column */}
            <Card className="kanban-column">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    Done
                  </span>
                  <Badge variant="secondary">{doneTasks.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {doneTasks.map((task) => (
                  <Card key={task.id} className="task-card opacity-75" data-testid={`task-${task.id}`}>
                    <CardContent className="p-4">
                      <h5 className="font-medium mb-2">{task.title}</h5>
                      {task.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mb-3">
                        <Badge className="bg-green-100 text-green-800">
                          Completed
                        </Badge>
                        {task.dueDate && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {task.assignee && (
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs text-primary-foreground mr-2">
                            {task.assignee.firstName?.[0] || task.assignee.username[0]}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {task.assignee.firstName && task.assignee.lastName
                              ? `${task.assignee.firstName} ${task.assignee.lastName}`
                              : task.assignee.username
                            }
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="members">
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {project.members.map((member, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 bg-muted rounded-lg" data-testid={`member-${index}`}>
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-medium">
                      {member.user.firstName?.[0] || member.user.username[0]}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">
                        {member.user.firstName && member.user.lastName
                          ? `${member.user.firstName} ${member.user.lastName}`
                          : member.user.username
                        }
                      </h5>
                      <p className="text-sm text-muted-foreground">
                        {member.user.role === "leader" ? "Team Leader" : "Employee"}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
