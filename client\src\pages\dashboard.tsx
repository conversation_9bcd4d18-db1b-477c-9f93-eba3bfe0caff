import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { authenticatedApiRequest } from "@/lib/auth";
import { Link } from "wouter";
import { 
  <PERSON>older<PERSON>pen, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  TrendingUp,
  ArrowRight
} from "lucide-react";

interface DashboardStats {
  activeProjects: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  progress: number;
  status: string;
  owner: {
    firstName?: string;
    lastName?: string;
    username: string;
  };
  memberCount: number;
  taskCount: number;
}

interface Task {
  id: string;
  title: string;
  status: string;
  priority: string;
  dueDate?: string;
  project: {
    name: string;
  };
  assignee?: {
    firstName?: string;
    lastName?: string;
    username: string;
  };
}

export default function Dashboard() {
  const { data: stats, isLoading: statsLoading } = useQuery<DashboardStats>({
    queryKey: ["dashboard", "stats"],
    queryFn: async () => {
      const response = await authenticatedApiRequest("GET", "/api/dashboard/stats");
      return response.json();
    },
  });

  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ["projects"],
    queryFn: async () => {
      const response = await authenticatedApiRequest("GET", "/api/projects");
      return response.json();
    },
  });

  const { data: myTasks, isLoading: tasksLoading } = useQuery<Task[]>({
    queryKey: ["tasks", "my"],
    queryFn: async () => {
      const response = await authenticatedApiRequest("GET", "/api/tasks");
      return response.json();
    },
  });

  const recentProjects = projects?.slice(0, 3) || [];
  const upcomingTasks = myTasks?.filter(task => 
    task.status !== "done" && task.dueDate
  ).slice(0, 5) || [];

  if (statsLoading || projectsLoading || tasksLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card data-testid="stat-active-projects">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm">Active Projects</p>
                <p className="text-2xl font-semibold">{stats?.activeProjects || 0}</p>
              </div>
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <FolderOpen className="text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              Projects in progress
            </div>
          </CardContent>
        </Card>
        
        <Card data-testid="stat-completed-tasks">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm">Completed Tasks</p>
                <p className="text-2xl font-semibold">{stats?.completedTasks || 0}</p>
              </div>
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              Tasks finished
            </div>
          </CardContent>
        </Card>
        
        <Card data-testid="stat-in-progress">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm">In Progress</p>
                <p className="text-2xl font-semibold">{stats?.inProgressTasks || 0}</p>
              </div>
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Clock className="text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-blue-600">
              <Clock className="w-4 h-4 mr-1" />
              Currently working
            </div>
          </CardContent>
        </Card>
        
        <Card data-testid="stat-overdue">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm">Overdue Tasks</p>
                <p className="text-2xl font-semibold">{stats?.overdueTasks || 0}</p>
              </div>
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-red-600">
              <AlertTriangle className="w-4 h-4 mr-1" />
              Need attention
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Projects</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentProjects.length === 0 ? (
              <div className="text-center py-8">
                <FolderOpen className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No projects yet</p>
                <Link href="/projects">
                  <Button size="sm" className="mt-2">Create Project</Button>
                </Link>
              </div>
            ) : (
              recentProjects.map((project) => (
                <div key={project.id} className="space-y-2" data-testid={`project-${project.id}`}>
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{project.name}</span>
                    <span>{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{project.taskCount} tasks</span>
                    <span>{project.memberCount} members</span>
                  </div>
                </div>
              ))
            )}
            {recentProjects.length > 0 && (
              <Link href="/projects">
                <Button variant="ghost" size="sm" className="w-full">
                  View All Projects
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>

        {/* My Tasks Preview */}
        <Card>
          <CardHeader>
            <CardTitle>My Upcoming Tasks</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {upcomingTasks.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No upcoming tasks</p>
                <Link href="/tasks">
                  <Button size="sm" className="mt-2">View Tasks</Button>
                </Link>
              </div>
            ) : (
              upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 bg-muted rounded-lg" data-testid={`task-${task.id}`}>
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      task.priority === "high" ? "bg-red-500" :
                      task.priority === "medium" ? "bg-yellow-500" : "bg-green-500"
                    }`} />
                    <div>
                      <p className="font-medium text-sm">{task.title}</p>
                      <p className="text-xs text-muted-foreground">{task.project.name}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">
                      {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : "No due date"}
                    </p>
                  </div>
                </div>
              ))
            )}
            {upcomingTasks.length > 0 && (
              <Link href="/tasks">
                <Button variant="ghost" size="sm" className="w-full">
                  View All Tasks
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
