import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { authenticatedApiRequest } from "@/lib/auth";
import { ProgressChart } from "@/components/charts/progress-chart";
import { 
  Download, 
  TrendingUp, 
  Clock, 
  Users, 
  CheckCircle,
  Calendar,
  BarChart3,
  PieChart
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ReportData {
  taskCompletionRate: number;
  avgDaysPerTask: number;
  activeMembers: number;
  productivityIncrease: number;
  teamPerformance: Array<{
    id: string;
    name: string;
    activeTasks: number;
    completedTasks: number;
    overdueTasks: number;
    completionRate: number;
    avgTime: string;
  }>;
  projectTimeline: Array<{
    id: string;
    name: string;
    progress: number;
    assignee: string;
    startDate: string;
    endDate: string;
  }>;
}

export default function Reports() {
  const [timeRange, setTimeRange] = useState("30");

  const { data: reportData, isLoading } = useQuery<ReportData>({
    queryKey: ["reports", timeRange],
    queryFn: async () => {
      // Since we don't have a reports API endpoint yet, return mock data
      // In a real implementation, this would fetch from /api/reports?range=${timeRange}
      return {
        taskCompletionRate: 89,
        avgDaysPerTask: 2.3,
        activeMembers: 12,
        productivityIncrease: 15,
        teamPerformance: [
          {
            id: "1",
            name: "Sarah Johnson",
            activeTasks: 8,
            completedTasks: 24,
            overdueTasks: 1,
            completionRate: 94,
            avgTime: "2.1 days",
          },
          {
            id: "2", 
            name: "Mike Chen",
            activeTasks: 12,
            completedTasks: 31,
            overdueTasks: 2,
            completionRate: 89,
            avgTime: "2.8 days",
          },
          {
            id: "3",
            name: "Emily Davis",
            activeTasks: 6,
            completedTasks: 18,
            overdueTasks: 0,
            completionRate: 97,
            avgTime: "1.9 days",
          },
        ],
        projectTimeline: [
          {
            id: "1",
            name: "E-commerce Platform",
            progress: 85,
            assignee: "Sarah Chen",
            startDate: "2024-01-01",
            endDate: "2024-12-15",
          },
          {
            id: "2",
            name: "Mobile App Redesign",
            progress: 60,
            assignee: "Alex Rodriguez", 
            startDate: "2024-02-01",
            endDate: "2025-01-15",
          },
          {
            id: "3",
            name: "API Integration",
            progress: 30,
            assignee: "Mike Johnson",
            startDate: "2024-03-01",
            endDate: "2025-02-28",
          },
        ],
      };
    },
  });

  const handleExport = () => {
    // In a real implementation, this would generate and download a report
    alert("Export functionality would be implemented here");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Reports & Analytics</h3>
          <p className="text-muted-foreground">Track progress and team performance</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-48" data-testid="select-time-range">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">This year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExport} data-testid="button-export">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card data-testid="metric-completion-rate">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="text-primary text-xl" />
            </div>
            <p className="text-2xl font-semibold mb-1">{reportData?.taskCompletionRate}%</p>
            <p className="text-sm text-muted-foreground">Task Completion Rate</p>
          </CardContent>
        </Card>
        
        <Card data-testid="metric-avg-time">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Clock className="text-green-600 text-xl" />
            </div>
            <p className="text-2xl font-semibold mb-1">{reportData?.avgDaysPerTask}</p>
            <p className="text-sm text-muted-foreground">Avg Days per Task</p>
          </CardContent>
        </Card>
        
        <Card data-testid="metric-active-members">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Users className="text-blue-600 text-xl" />
            </div>
            <p className="text-2xl font-semibold mb-1">{reportData?.activeMembers}</p>
            <p className="text-sm text-muted-foreground">Active Team Members</p>
          </CardContent>
        </Card>
        
        <Card data-testid="metric-productivity">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="text-amber-600 text-xl" />
            </div>
            <p className="text-2xl font-semibold mb-1">{reportData?.productivityIncrease}%</p>
            <p className="text-sm text-muted-foreground">Productivity Increase</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="w-5 h-5 mr-2" />
              Task Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ProgressChart />
          </CardContent>
        </Card>

        {/* Team Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Team Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {reportData?.teamPerformance.map((member) => (
              <div key={member.id} className="flex items-center justify-between" data-testid={`performance-${member.id}`}>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <span className="font-medium">{member.name}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-24 bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${member.completionRate}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium w-8">{member.completionRate}%</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Project Timeline (Gantt Chart) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Project Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-12 gap-2 text-xs text-muted-foreground mb-4">
              <div className="col-span-3"></div>
              <div className="text-center">Dec 1</div>
              <div className="text-center">Dec 8</div>
              <div className="text-center">Dec 15</div>
              <div className="text-center">Dec 22</div>
              <div className="text-center">Dec 29</div>
              <div className="text-center">Jan 5</div>
              <div className="text-center">Jan 12</div>
              <div className="text-center">Jan 19</div>
              <div className="text-center">Jan 26</div>
            </div>
            
            {reportData?.projectTimeline.map((project) => (
              <div key={project.id} className="grid grid-cols-12 gap-2 items-center py-2" data-testid={`timeline-${project.id}`}>
                <div className="col-span-3">
                  <p className="text-sm font-medium">{project.name}</p>
                  <p className="text-xs text-muted-foreground">{project.assignee}</p>
                </div>
                <div className="col-span-9 relative">
                  <div className="h-6 bg-muted rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary rounded-full" 
                      style={{ 
                        width: `${project.progress}%`, 
                        marginLeft: Math.random() * 20 + '%' // Mock positioning
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Performance Report</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Team Member</TableHead>
                <TableHead>Active Tasks</TableHead>
                <TableHead>Completed</TableHead>
                <TableHead>Overdue</TableHead>
                <TableHead>Completion Rate</TableHead>
                <TableHead>Avg Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData?.teamPerformance.map((member) => (
                <TableRow key={member.id} data-testid={`performance-row-${member.id}`}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <span>{member.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{member.activeTasks}</TableCell>
                  <TableCell>{member.completedTasks}</TableCell>
                  <TableCell>
                    <span className={member.overdueTasks > 0 ? "text-red-600" : ""}>
                      {member.overdueTasks}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{member.completionRate}%</span>
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${member.completionRate}%` }}
                        ></div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{member.avgTime}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
