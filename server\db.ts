import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

// Temporary: Use a default database URL for development
const DATABASE_URL = process.env.DATABASE_URL || "postgres://postgres:postgres@localhost:5432/flowforge";

console.log("Attempting to connect to database:", DATABASE_URL);

export const pool = new Pool({ connectionString: DATABASE_URL });

export const db = drizzle({ client: pool, schema });