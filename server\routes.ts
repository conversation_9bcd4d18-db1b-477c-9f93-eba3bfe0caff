import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, loginSchema, insertProjectSchema, insertTaskSchema, insertTaskCommentSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import { Request, Response, NextFunction } from "express";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

interface AuthRequest extends Request {
  user?: { id: string; role: string };
}

// Middleware to verify JWT token
const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth routes
  app.post('/api/auth/register', async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      
      // Check if user already exists
      const existingUser = await storage.getUserByEmail(userData.email);
      if (existingUser) {
        return res.status(400).json({ message: 'User already exists' });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      // Create user
      const user = await storage.createUser({
        ...userData,
        password: hashedPassword,
      });

      // Generate token
      const token = jwt.sign(
        { id: user.id, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        user: { ...user, password: undefined },
        token,
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(400).json({ message: 'Registration failed' });
    }
  });

  app.post('/api/auth/login', async (req, res) => {
    try {
      const { email, password } = loginSchema.parse(req.body);
      
      // Find user by email or username
      let user = await storage.getUserByEmail(email);
      if (!user) {
        user = await storage.getUserByUsername(email);
      }
      
      if (!user) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // Check password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // Generate token
      const token = jwt.sign(
        { id: user.id, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        user: { ...user, password: undefined },
        token,
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(400).json({ message: 'Login failed' });
    }
  });

  app.get('/api/auth/me', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      res.json({ ...user, password: undefined });
    } catch (error) {
      console.error('Get user error:', error);
      res.status(500).json({ message: 'Failed to get user' });
    }
  });

  // Dashboard routes
  app.get('/api/dashboard/stats', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const stats = await storage.getDashboardStats(req.user!.id);
      res.json(stats);
    } catch (error) {
      console.error('Dashboard stats error:', error);
      res.status(500).json({ message: 'Failed to get dashboard stats' });
    }
  });

  // Project routes
  app.get('/api/projects', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const projects = await storage.getProjects(req.user!.id);
      res.json(projects);
    } catch (error) {
      console.error('Get projects error:', error);
      res.status(500).json({ message: 'Failed to get projects' });
    }
  });

  app.get('/api/projects/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const project = await storage.getProject(req.params.id);
      if (!project) {
        return res.status(404).json({ message: 'Project not found' });
      }
      res.json(project);
    } catch (error) {
      console.error('Get project error:', error);
      res.status(500).json({ message: 'Failed to get project' });
    }
  });

  app.post('/api/projects', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const projectData = insertProjectSchema.parse({
        ...req.body,
        ownerId: req.user!.id,
      });
      
      const project = await storage.createProject(projectData);
      res.json(project);
    } catch (error) {
      console.error('Create project error:', error);
      res.status(400).json({ message: 'Failed to create project' });
    }
  });

  app.put('/api/projects/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const updates = insertProjectSchema.partial().parse(req.body);
      const project = await storage.updateProject(req.params.id, updates);
      res.json(project);
    } catch (error) {
      console.error('Update project error:', error);
      res.status(400).json({ message: 'Failed to update project' });
    }
  });

  app.delete('/api/projects/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      await storage.deleteProject(req.params.id);
      res.json({ message: 'Project deleted successfully' });
    } catch (error) {
      console.error('Delete project error:', error);
      res.status(500).json({ message: 'Failed to delete project' });
    }
  });

  // Project member routes
  app.post('/api/projects/:id/members', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const { userId } = req.body;
      const member = await storage.addProjectMember({
        projectId: req.params.id,
        userId,
      });
      res.json(member);
    } catch (error) {
      console.error('Add project member error:', error);
      res.status(400).json({ message: 'Failed to add project member' });
    }
  });

  app.delete('/api/projects/:projectId/members/:userId', authenticateToken, async (req: AuthRequest, res) => {
    try {
      await storage.removeProjectMember(req.params.projectId, req.params.userId);
      res.json({ message: 'Member removed successfully' });
    } catch (error) {
      console.error('Remove project member error:', error);
      res.status(500).json({ message: 'Failed to remove member' });
    }
  });

  app.get('/api/projects/:id/members', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const members = await storage.getProjectMembers(req.params.id);
      res.json(members);
    } catch (error) {
      console.error('Get project members error:', error);
      res.status(500).json({ message: 'Failed to get project members' });
    }
  });

  // Task routes
  app.get('/api/tasks', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const { projectId, assigneeId } = req.query;
      const tasks = await storage.getTasks(
        projectId as string,
        assigneeId as string
      );
      res.json(tasks);
    } catch (error) {
      console.error('Get tasks error:', error);
      res.status(500).json({ message: 'Failed to get tasks' });
    }
  });

  app.get('/api/tasks/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const task = await storage.getTask(req.params.id);
      if (!task) {
        return res.status(404).json({ message: 'Task not found' });
      }
      res.json(task);
    } catch (error) {
      console.error('Get task error:', error);
      res.status(500).json({ message: 'Failed to get task' });
    }
  });

  app.post('/api/tasks', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const taskData = insertTaskSchema.parse({
        ...req.body,
        createdById: req.user!.id,
      });
      
      const task = await storage.createTask(taskData);
      
      // Create notification if task is assigned to someone
      if (task.assigneeId && task.assigneeId !== req.user!.id) {
        await storage.createNotification({
          userId: task.assigneeId,
          type: 'task_assigned',
          title: 'New Task Assigned',
          message: `You have been assigned a new task: ${task.title}`,
          relatedTaskId: task.id,
        });
      }
      
      res.json(task);
    } catch (error) {
      console.error('Create task error:', error);
      res.status(400).json({ message: 'Failed to create task' });
    }
  });

  app.put('/api/tasks/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const updates = insertTaskSchema.partial().parse(req.body);
      const task = await storage.updateTask(req.params.id, updates);
      res.json(task);
    } catch (error) {
      console.error('Update task error:', error);
      res.status(400).json({ message: 'Failed to update task' });
    }
  });

  app.delete('/api/tasks/:id', authenticateToken, async (req: AuthRequest, res) => {
    try {
      await storage.deleteTask(req.params.id);
      res.json({ message: 'Task deleted successfully' });
    } catch (error) {
      console.error('Delete task error:', error);
      res.status(500).json({ message: 'Failed to delete task' });
    }
  });

  // Task comment routes
  app.get('/api/tasks/:id/comments', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const comments = await storage.getTaskComments(req.params.id);
      res.json(comments);
    } catch (error) {
      console.error('Get task comments error:', error);
      res.status(500).json({ message: 'Failed to get task comments' });
    }
  });

  app.post('/api/tasks/:id/comments', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const commentData = insertTaskCommentSchema.parse({
        ...req.body,
        taskId: req.params.id,
        userId: req.user!.id,
      });
      
      const comment = await storage.addTaskComment(commentData);
      res.json(comment);
    } catch (error) {
      console.error('Add task comment error:', error);
      res.status(400).json({ message: 'Failed to add comment' });
    }
  });

  // Notification routes
  app.get('/api/notifications', authenticateToken, async (req: AuthRequest, res) => {
    try {
      const notifications = await storage.getNotifications(req.user!.id);
      res.json(notifications);
    } catch (error) {
      console.error('Get notifications error:', error);
      res.status(500).json({ message: 'Failed to get notifications' });
    }
  });

  app.put('/api/notifications/:id/read', authenticateToken, async (req: AuthRequest, res) => {
    try {
      await storage.markNotificationAsRead(req.params.id);
      res.json({ message: 'Notification marked as read' });
    } catch (error) {
      console.error('Mark notification as read error:', error);
      res.status(500).json({ message: 'Failed to mark notification as read' });
    }
  });

  app.put('/api/notifications/read-all', authenticateToken, async (req: AuthRequest, res) => {
    try {
      await storage.markAllNotificationsAsRead(req.user!.id);
      res.json({ message: 'All notifications marked as read' });
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      res.status(500).json({ message: 'Failed to mark all notifications as read' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
