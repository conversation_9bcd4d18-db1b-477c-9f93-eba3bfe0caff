import { apiRequest } from "./queryClient";

export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  role: "leader" | "employee";
  profileImageUrl?: string;
  department?: string;
  isActive: boolean;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName?: string;
  lastName?: string;
  role: "leader" | "employee";
}

export interface AuthResponse {
  user: User;
  token: string;
}

const TOKEN_KEY = "auth_token";

export const authAPI = {
  async login(data: LoginData): Promise<AuthResponse> {
    const response = await apiRequest("POST", "/api/auth/login", data);
    const authData: AuthResponse = await response.json();
    localStorage.setItem(TOKEN_KEY, authData.token);
    return authData;
  },

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await apiRequest("POST", "/api/auth/register", data);
    const authData: AuthResponse = await response.json();
    localStorage.setItem(TOKEN_KEY, authData.token);
    return authData;
  },

  async getCurrentUser(): Promise<User> {
    const token = localStorage.getItem(TOKEN_KEY);
    if (!token) {
      throw new Error("No token found");
    }

    const response = await fetch("/api/auth/me", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        localStorage.removeItem(TOKEN_KEY);
        throw new Error("Unauthorized");
      }
      throw new Error("Failed to get user");
    }

    return await response.json();
  },

  logout() {
    localStorage.removeItem(TOKEN_KEY);
  },

  getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  },
};

// Add token to all API requests
const originalApiRequest = apiRequest;
export { originalApiRequest as baseApiRequest };

export async function authenticatedApiRequest(
  method: string,
  url: string,
  data?: unknown,
): Promise<Response> {
  const token = authAPI.getToken();
  const headers: Record<string, string> = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  if (data) {
    headers["Content-Type"] = "application/json";
  }

  const res = await fetch(url, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    if (res.status === 401) {
      authAPI.logout();
      window.location.href = "/";
    }
    throw new Error(`${res.status}: ${text}`);
  }

  return res;
}
