import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TaskCard } from "./task-card";

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: string;
  assignee?: {
    firstName?: string;
    lastName?: string;
    username: string;
  };
  project: {
    id: string;
    name: string;
  };
}

interface KanbanBoardProps {
  tasks: Task[];
  onStatusChange: (taskId: string, newStatus: string) => void;
}

const statusColumns = [
  {
    id: "todo",
    title: "To Do",
    color: "bg-gray-400",
  },
  {
    id: "in_progress", 
    title: "In Progress",
    color: "bg-blue-500",
  },
  {
    id: "review",
    title: "Review", 
    color: "bg-purple-500",
  },
  {
    id: "done",
    title: "Done",
    color: "bg-green-500",
  },
];

export function KanbanBoard({ tasks, onStatusChange }: KanbanBoardProps) {
  const getTasksByStatus = (status: string) => {
    return tasks.filter(task => task.status === status);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6" data-testid="kanban-board">
      {statusColumns.map((column) => {
        const columnTasks = getTasksByStatus(column.id);
        
        return (
          <Card key={column.id} className="kanban-column">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center justify-between">
                <span className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${column.color} mr-2`}></div>
                  {column.title}
                </span>
                <Badge variant="secondary">{columnTasks.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {columnTasks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground text-sm">
                  No tasks
                </div>
              ) : (
                columnTasks.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onStatusChange={onStatusChange}
                  />
                ))
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
